import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import { openai } from "@ai-sdk/openai";
import dotenv from "dotenv";

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: '*', // Mobil uygulama için tüm origin'lere izin ver
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// MCP client
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/book_mcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79"),
    },
  },
});

// Global agent variable
let agent: Agent | null = null;

// Agent oluşturma fonksiyonu
async function initializeAgent() {
  if (agent) return agent;

  try {
    console.log("🔧 Agent başlatılıyor...");
    const tools = await mcp.getTools();

    agent = new Agent({
      name: "BookSearchAgent",
      model: openai("gpt-4o-mini"),
      instructions: `You are a helpful book search assistant. You have access to MCP tools that can search for books.
      When users ask about books, use the available MCP tools to search and provide relevant information.
      Always be helpful and provide detailed responses about the books you find.`,
      tools: tools,
    });

    console.log("✅ Agent hazır!");
    return agent;
  } catch (error) {
    console.error("❌ Agent başlatma hatası:", error);
    throw error;
  }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Book Search API is running',
    timestamp: new Date().toISOString()
  });
});

// Chat endpoint - mobil uygulama için ana endpoint
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;

    // Input validation
    if (!message || typeof message !== 'string' || message.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Mesaj gerekli ve boş olamaz'
      });
    }

    // Agent'ı başlat (eğer başlatılmamışsa)
    await initializeAgent();

    if (!agent) {
      return res.status(500).json({
        success: false,
        error: 'Agent başlatılamadı'
      });
    }

    console.log(`📱 Mobil uygulama mesajı: ${message}`);

    // Agent'tan cevap al
    const response = await agent.generate(message.trim());

    console.log(`🤖 Agent cevabı: ${response.text.substring(0, 100)}...`);

    // Başarılı response
    res.json({
      success: true,
      data: {
        message: response.text,
        timestamp: new Date().toISOString(),
        usage: response.usage || null
      }
    });

  } catch (error) {
    console.error("❌ Chat endpoint hatası:", error);

    res.status(500).json({
      success: false,
      error: 'Bir hata oluştu. Lütfen tekrar deneyin.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Agent status endpoint
app.get('/api/agent/status', async (req, res) => {
  try {
    const isReady = agent !== null;

    res.json({
      success: true,
      data: {
        isReady,
        agentName: agent?.name || null,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Agent durumu kontrol edilemedi'
    });
  }
});

// Agent'ı yeniden başlatma endpoint (geliştirme için)
app.post('/api/agent/restart', async (req, res) => {
  try {
    agent = null;
    await initializeAgent();

    res.json({
      success: true,
      message: 'Agent başarıyla yeniden başlatıldı'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Agent yeniden başlatılamadı'
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint bulunamadı'
  });
});

// Error handler
app.use((error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Sunucu hatası:', error);

  res.status(500).json({
    success: false,
    error: 'Sunucu hatası',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// Server'ı başlat
async function startServer() {
  try {
    // Agent'ı önceden başlat
    await initializeAgent();

    app.listen(PORT, () => {
      console.log(`🚀 API Server çalışıyor: http://localhost:${PORT}`);
      console.log(`📱 Mobil uygulama endpoint'i: http://localhost:${PORT}/api/chat`);
      console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error("❌ Server başlatma hatası:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Server kapatılıyor...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Server kapatılıyor...');
  process.exit(0);
});

// Server'ı başlat
startServer();

export { app, initializeAgent };
