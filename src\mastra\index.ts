
import { <PERSON><PERSON> } from '@mastra/core';
import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import { openai } from "@ai-sdk/openai";
import dotenv from "dotenv";

dotenv.config();

// MCP client - kendi MCP endpoint'in ve api_key'inle de<PERSON>r
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/book_mcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79"),
    },
  },
});

// Book Search Agent - tools will be loaded lazily
const bookSearchAgent = new Agent({
  name: "BookSearchAgent",
  model: openai("gpt-4o-mini"),
  instructions: `You are a helpful book search assistant. You have access to MCP tools that can search for books.
  When users ask about books, use the available MCP tools to search and provide relevant information.
  Always be helpful and provide detailed responses about the books you find.`,
  tools: async () => await mcp.getTools(),
});

export const mastra = new Mastra({
  agents: {
    bookSearchAgent,
  },
});
