# 📚 Kitap Arama Asistanı - Tam Entegra<PERSON>

Bu proje, Mastra Agent framework'ü kullanarak MCP (Model Context Protocol) ile entegre edilmiş bir kitap arama asistanı ve mobil/web arayüzlerini içerir.

## 🏗️ Proje <PERSON>

```
book_app/
├── agent.ts                 # Standalone console agent
├── src/
│   ├── api/server.ts       # REST API server
│   └── mastra/
│       ├── index.ts        # Mastra framework konfigürasyonu
│       └── agents/agent.ts # Mastra agent tanımı
├── mobile-app/             # React Native (Expo) mobil uygulama
│   ├── App.js
│   ├── package.json
│   └── README.md
├── web-app/                # Web tabanlı test arayüzü
│   └── index.html
└── test-api.js            # API test script'i
```

## 🚀 Hızlı Başlangıç

### 1. Bağımlılıkları Yükleyin
```bash
npm install
```

### 2. Environment Ayarları
`.env` dosyasında OpenAI API key'iniz mevcut:
```
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Farklı Modlarda Çalıştırın

#### Console Agent (Basit Chat)
```bash
npm run dev
```

#### Mastra Playground
```bash
npm run dev:mastra
```
Sonra http://localhost:4111/ adresine gidin.

#### API Server (Mobil/Web için)
```bash
npm run dev:api
```
API http://localhost:3001 adresinde çalışır.

## 📱 Mobil Uygulama

### React Native (Expo) Uygulaması
```bash
cd mobile-app
npm install
npm start
```

Detaylı kurulum için: [mobile-app/README.md](mobile-app/README.md)

## 🌐 Web Arayüzü

Basit web tabanlı test arayüzü:
```bash
# API server'ını başlatın
npm run dev:api

# Sonra web-app/index.html dosyasını browser'da açın
```

## 🔧 API Endpoints

### Base URL: `http://localhost:3001`

#### Health Check
```
GET /api/health
```

#### Chat
```
POST /api/chat
Content-Type: application/json

{
  "message": "Harry Potter kitapları hakkında bilgi ver"
}
```

#### Agent Status
```
GET /api/agent/status
```

#### Agent Restart
```
POST /api/agent/restart
```

## 🛠️ Özellikler

### ✅ Tamamlanan
- **Mastra Agent**: OpenAI GPT-4o-mini ile MCP entegrasyonu
- **MCP Integration**: Smithery book search endpoint'i
- **Console Interface**: Interaktif terminal chat
- **REST API**: Mobil/web uygulamalar için
- **Web App**: Basit HTML/CSS/JS arayüzü
- **Mobile App**: React Native (Expo) uygulaması
- **Error Handling**: Kapsamlı hata yönetimi
- **CORS Support**: Cross-origin istekler için

### 🎯 Kullanım Senaryoları

1. **Geliştirici Testi**: Console agent ile hızlı test
2. **Demo**: Mastra playground ile görsel demo
3. **Mobil Entegrasyon**: React Native uygulaması
4. **Web Entegrasyon**: HTML/JS ile basit entegrasyon
5. **API Entegrasyon**: REST API ile herhangi bir platform

## 🔍 Test Etme

### API Testi
```bash
node test-api.js
```

### Manuel Test
```bash
# Terminal 1: API Server
npm run dev:api

# Terminal 2: Test
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Tolkien kitapları ara"}'
```

## 📋 Gereksinimler

- Node.js 20+
- npm veya yarn
- OpenAI API key
- MCP endpoint (mevcut: Smithery book search)

## 🔐 Güvenlik Notları

- **Development Only**: Bu konfigürasyon geliştirme amaçlıdır
- **API Keys**: Production'da environment variables kullanın
- **CORS**: Production'da specific origin'lere kısıtlayın
- **Rate Limiting**: Production'da rate limiting ekleyin
- **HTTPS**: Production'da HTTPS kullanın

## 🐛 Sorun Giderme

### "Agent başlatılamadı" hatası
- OpenAI API key'ini kontrol edin
- MCP endpoint'inin erişilebilir olduğunu kontrol edin

### "Network request failed" (Mobil)
- API server'ının çalıştığından emin olun
- IP adresini kontrol edin (localhost yerine gerçek IP)
- Firewall ayarlarını kontrol edin

### CORS hatası
- API server'ında CORS ayarları kontrol edin
- Origin'lerin doğru tanımlandığından emin olun

## 📞 Destek

Herhangi bir sorun için:
1. Console log'larını kontrol edin
2. API health check'ini test edin: `http://localhost:3001/api/health`
3. MCP endpoint'inin çalıştığını doğrulayın

## 🎉 Sonuç

Artık agent'ınız:
- ✅ Console'da çalışıyor
- ✅ Mastra playground'unda çalışıyor  
- ✅ REST API olarak çalışıyor
- ✅ Mobil uygulamaya bağlı
- ✅ Web arayüzüne bağlı
- ✅ MCP tool'larını kullanıyor

Tüm platformlarda kitap arama işlevselliği tam olarak çalışmaktadır! 🚀
