import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import { openai } from "@ai-sdk/openai";
import dotenv from "dotenv";
import * as readline from "readline";

dotenv.config();

// MCP client - kendi MCP endpoint'in ve api_key'inle değiştir
const mcp = new MCPClient({
  servers: {
    smithery: {
      url: new URL("https://server.smithery.ai/@ceydasimsekk/book_mcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79"),
    },
  },
});

// Global agent variable
let agent: Agent;

// Agent tanımı - async function içinde oluşturacağız
async function createAgent() {
  const tools = await mcp.getTools();

  agent = new Agent({
    name: "BookSearchAgent",
    model: openai("gpt-4o-mini"),
    instructions: `You are a helpful book search assistant. You have access to MCP tools that can search for books.
    When users ask about books, use the available MCP tools to search and provide relevant information.
    Always be helpful and provide detailed responses about the books you find.`,
    tools: tools,
  });

  return agent;
}

// Interactive chat interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log("🤖 Book Search Agent başlatıldı!");
console.log("📚 Kitap arama konusunda size yardımcı olabilirim.");
console.log("💬 Bir şeyler yazın (çıkmak için 'exit' yazın):\n");

async function startChat() {
  // Agent'ı başlat
  console.log("🔧 Agent başlatılıyor...");
  await createAgent();
  console.log("✅ Agent hazır!");

  const askQuestion = () => {
    rl.question("Sen: ", async (input) => {
      if (input.toLowerCase() === "exit") {
        console.log("👋 Görüşürüz!");
        rl.close();
        return;
      }

      if (input.trim() === "") {
        askQuestion();
        return;
      }

      try {
        console.log("🤔 Düşünüyorum...");

        const response = await agent.generate(input);

        console.log("\n🤖 Agent:", response);
        console.log("\n" + "─".repeat(50) + "\n");

        askQuestion();
      } catch (error) {
        console.error("❌ Hata:", error);
        console.log("\n" + "─".repeat(50) + "\n");
        askQuestion();
      }
    });
  };

  askQuestion();
}

// Test fonksiyonu - geliştirme amaçlı
async function test() {
  try {
    console.log("🧪 Test başlatılıyor...");
    await createAgent();
    const response = await agent.generate('Search for books about "harry potter" using available tools.');
    console.log("✅ Test sonucu:", response);
  } catch (error) {
    console.error("❌ Test hatası:", error);
  }
}

// Eğer bu dosya doğrudan çalıştırılıyorsa chat'i başlat
if (process.argv[1] && process.argv[1].endsWith('agent.ts')) {
  startChat();
}

export { agent, test };
