import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import OpenAI from "openai";
import dotenv from "dotenv";

dotenv.config();

// MCP client - kendi MCP endpoint'in ve api_key'inle değ<PERSON>ştir
const mcp = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@ceydasimsekk/book_mcp/mcp?api_key=120a45fb-730a-4de8-856c-bb26491add79",
    },
  },
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Agent tanımı
const agent = new Agent({
  client: openai,
  mcpClient: mcp,
  name: "BookSearchAgent",
  model: "gpt-4o-mini",
});

async function test() {
  // MCP tool ismi ve input parametresi:
  const response = await agent.invoke({
    prompt: 'Search for books about "harry potter" using the MCP tool "search_books_tool".',
  });
  console.log(response);
}

test();
