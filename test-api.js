// API test script
async function testAPI() {
  const baseURL = 'http://localhost:3001';
  
  try {
    console.log('🧪 API Test başlatılıyor...\n');
    
    // 1. Health check
    console.log('1️⃣ Health check testi...');
    const healthResponse = await fetch(`${baseURL}/api/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    console.log('');
    
    // 2. Chat endpoint test
    console.log('2️⃣ Chat endpoint testi...');
    const chatResponse = await fetch(`${baseURL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Harry Potter kitapları hakkında bilgi ver'
      })
    });
    
    const chatData = await chatResponse.json();
    console.log('✅ Chat response:', {
      success: chatData.success,
      messageLength: chatData.data?.message?.length || 0,
      timestamp: chatData.data?.timestamp
    });
    
    if (chatData.data?.message) {
      console.log('\n📖 Agent cevabı:');
      console.log(chatData.data.message.substring(0, 200) + '...');
    }
    
    console.log('\n🎉 Tüm testler başarılı!');
    
  } catch (error) {
    console.error('❌ Test hatası:', error.message);
  }
}

testAPI();
