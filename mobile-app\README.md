# 📚 Kitap Arama Asistanı - Mobil Uygulama

Bu mobil uygulama, Book Search Agent'ınızla konuşmanızı sağlayan basit bir arayüzdür.

## 🚀 Kurulum

### 1. Expo CLI'yi y<PERSON> (<PERSON><PERSON><PERSON> yoksa)
```bash
npm install -g expo-cli
```

### 2. Mobil uygulama klasörüne gidin
```bash
cd mobile-app
```

### 3. Bağımlılıkları yükleyin
```bash
npm install
```

### 4. API Server'ını başlatın
Ana proje klasöründe:
```bash
npm run dev:api
```

### 5. Mobil uygulamayı başlatın
```bash
npm start
```

## 📱 Test Etme

### Expo Go ile Test (En Kolay)
1. Telefonunuza **Expo Go** uygulamasını indirin
2. `npm start` komutunu çalıştırın
3. Terminal'de çıkan QR kodu telefonunuzla tarayın
4. Uygulama telefonunuzda açılacak

### Android Emulator ile Test
```bash
npm run android
```

### iOS Simulator ile Test (Mac gerekli)
```bash
npm run ios
```

## ⚙️ Konfigürasyon

### IP Adresi Ayarı
Gerçek cihazda test ederken, `App.js` dosyasındaki `API_BASE_URL`'yi değiştirin:

```javascript
// Localhost yerine bilgisayarınızın IP adresini kullanın
const API_BASE_URL = 'http://*************:3001';
```

IP adresinizi öğrenmek için:
- Windows: `ipconfig`
- Mac/Linux: `ifconfig`

## 🎯 Özellikler

- ✅ Basit ve temiz arayüz
- ✅ Gerçek zamanlı chat
- ✅ Loading göstergesi
- ✅ Hata yönetimi
- ✅ Konuşma geçmişi
- ✅ Temizleme butonu

## 🔧 Sorun Giderme

### "Network request failed" hatası
- API server'ının çalıştığından emin olun (`npm run dev:api`)
- IP adresinin doğru olduğunu kontrol edin
- Firewall ayarlarını kontrol edin

### Expo Go bağlantı sorunu
- Telefon ve bilgisayarın aynı WiFi ağında olduğundan emin olun
- VPN kapalı olduğundan emin olun

## 📝 Notlar

- Bu uygulama geliştirme amaçlıdır
- Production için güvenlik önlemleri eklenmeli
- API endpoint'leri HTTPS ile korunmalı
