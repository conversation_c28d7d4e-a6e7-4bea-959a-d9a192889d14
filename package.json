{"name": "book_app", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "tsx agent.ts", "dev:mastra": "<PERSON>ra dev", "build": "mastra build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.1", "@mastra/libsql": "^0.10.0", "@mastra/mcp": "^0.10.1", "@mastra/memory": "^0.10.1", "dotenv": "^16.5.0", "zod": "^3.25.32"}, "devDependencies": {"@types/node": "^22.15.23", "mastra": "^0.10.1", "tsx": "^4.19.4", "typescript": "^5.8.3"}}