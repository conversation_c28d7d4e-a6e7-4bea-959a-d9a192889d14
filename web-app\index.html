<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📚 Kitap Arama Asistanı</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            width: 90%;
            max-width: 400px;
            height: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: #007AFF;
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .clear-btn {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
        }

        .clear-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .chat-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .empty-state {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #666;
            line-height: 1.5;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .user-message {
            background: #007AFF;
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 5px;
        }

        .agent-message {
            background: #f1f1f1;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            color: #666;
        }

        .loading-dots {
            display: inline-block;
            margin-left: 10px;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .input-area {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .input-container {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            resize: none;
            max-height: 100px;
        }

        #messageInput:focus {
            border-color: #007AFF;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: #007AFF;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            background: #0056b3;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error {
            background: #ff4444;
            color: white;
            padding: 10px;
            margin: 10px 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Kitap Arama Asistanı</h1>
            <button class="clear-btn" onclick="clearChat()">Temizle</button>
        </div>

        <div class="chat-area" id="chatArea">
            <div class="empty-state" id="emptyState">
                Merhaba! 👋<br>
                Kitap arama konusunda size yardımcı olabilirim.<br>
                Aşağıdaki kutucuğa sorunuzu yazın.
            </div>
        </div>

        <div class="input-area">
            <div class="input-container">
                <textarea 
                    id="messageInput" 
                    placeholder="Kitap hakkında bir şey sorun..."
                    rows="1"
                    maxlength="500"
                ></textarea>
            </div>
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">📤</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001';
        let isLoading = false;

        // Auto-resize textarea
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Enter to send (Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        function addMessage(text, isUser = false) {
            const chatArea = document.getElementById('chatArea');
            const emptyState = document.getElementById('emptyState');
            
            if (emptyState) {
                emptyState.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'agent-message'}`;
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString('tr-TR', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div>${text}</div>
                <div class="message-time">${timeStr}</div>
            `;

            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function showLoading() {
            const chatArea = document.getElementById('chatArea');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading';
            loadingDiv.id = 'loadingIndicator';
            loadingDiv.innerHTML = '🤔 Agent düşünüyor<span class="loading-dots"></span>';
            chatArea.appendChild(loadingDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function hideLoading() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }

        function showError(message) {
            const container = document.querySelector('.container');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            container.insertBefore(errorDiv, container.querySelector('.input-area'));
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        async function sendMessage() {
            if (isLoading) return;

            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = messageInput.value.trim();

            if (!message) {
                alert('Lütfen bir mesaj yazın');
                return;
            }

            // UI updates
            isLoading = true;
            sendBtn.disabled = true;
            sendBtn.textContent = '⏳';
            messageInput.disabled = true;

            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show loading
            showLoading();

            try {
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message })
                });

                const data = await response.json();

                if (data.success) {
                    hideLoading();
                    addMessage(data.data.message);
                } else {
                    hideLoading();
                    showError(data.error || 'Bir hata oluştu');
                }
            } catch (error) {
                hideLoading();
                console.error('API Hatası:', error);
                showError('Sunucuya bağlanılamadı. API server\'ının çalıştığından emin olun.');
            } finally {
                // Reset UI
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '📤';
                messageInput.disabled = false;
                messageInput.focus();
            }
        }

        function clearChat() {
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="empty-state" id="emptyState">
                    Merhaba! 👋<br>
                    Kitap arama konusunda size yardımcı olabilirim.<br>
                    Aşağıdaki kutucuğa sorunuzu yazın.
                </div>
            `;
        }

        // Focus input on load
        window.addEventListener('load', () => {
            messageInput.focus();
        });
    </script>
</body>
</html>
